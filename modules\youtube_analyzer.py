# محلل محتوى يوتيوب لأخبار ماين كرافت
import re
import requests
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import json
from urllib.parse import quote
from .logger import logger
from config.settings import google_api_manager

class YouTubeAnalyzer:
    """محلل محتوى يوتيوب للحصول على أحدث أخبار الألعاب"""
    
    def __init__(self):
        if not google_api_manager:
            logger.warning("⚠️ مدير مفاتيح Google غير مهيأ، سيتم الاعتماد على البحث البديل.")
        self.base_url = "https://www.googleapis.com/youtube/v3"
        self.session = requests.Session()
        
        # قنوات يوتيوب المتخصصة
        self.gaming_channels = [
            "IGN",
            "GameSpot",
            "PlayStation",
            "Xbox",
            "Nintendo",
            "PC Gamer",
            "GamesRadar"
        ]

        # كلمات البحث لمحتوى الألعاب
        self.search_queries = [
            "new game trailers 2025",
            "upcoming games 2025",
            "video game news",
            "أخبار ألعاب الفيديو",
            "game reviews",
            "مراجعات ألعاب",
            "gameplay footage",
            "new game releases",
            "e3 2025",
            "gamescom 2025"
        ]

        self.archive_search_queries = [
            "history of video games",
            "evolution of gaming",
            "best games of all time",
            "top 10 rpgs",
            "history of fps games"
        ]
    
    def search_videos(self, max_results: int = 20) -> List[Dict]:
        """البحث عن فيديوهات الألعاب الحديثة والقديمة عند الحاجة"""
        all_videos = []
        
        # البحث عن محتوى جديد أولاً
        for query in self.search_queries:
            try:
                videos = self._search_videos_by_query(query, max_results // len(self.search_queries))
                all_videos.extend(videos)
                logger.info(f"🔍 تم العثور على {len(videos)} فيديو للبحث (جديد): {query}")
            except Exception as e:
                logger.error(f"❌ فشل البحث عن الفيديوهات للاستعلام: {query}", e)
        
        unique_videos = self._remove_duplicate_videos(all_videos)
        
        # إذا لم يتم العثور على محتوى جديد، ابحث في الأرشيف
        if not unique_videos:
            logger.info("ℹ️ لم يتم العثور على محتوى جديد، جاري البحث في الأرشيف...")
            for query in self.archive_search_queries:
                try:
                    videos = self._search_videos_by_query(query, max_results // len(self.archive_search_queries), archive=True)
                    all_videos.extend(videos)
                    logger.info(f"🔍 تم العثور على {len(videos)} فيديو للبحث (أرشيف): {query}")
                except Exception as e:
                    logger.error(f"❌ فشل البحث عن الفيديوهات في الأرشيف: {query}", e)
            
            unique_videos = self._remove_duplicate_videos(all_videos)

        # ترتيب حسب التاريخ
        sorted_videos = sorted(unique_videos, key=lambda x: x.get('published_at', datetime.min), reverse=True)
        
        logger.info(f"📺 إجمالي الفيديوهات الفريدة: {len(sorted_videos)}")
        return sorted_videos[:max_results]
    
    def _search_videos_by_query(self, query: str, max_results: int = 10, archive: bool = False) -> List[Dict]:
        """البحث عن فيديوهات باستعلام محدد"""
        if not google_api_manager or not google_api_manager.get_key():
            # في حالة عدم وجود مفتاح API، استخدام البحث البديل
            logger.warning("⚠️ لا يوجد مفتاح YouTube API، سيتم استخدام البحث البديل.")
            return self._alternative_youtube_search(query, max_results)
        
        try:
            # البحث باستخدام YouTube Data API
            params = {
                'part': 'snippet',
                'q': query,
                'type': 'video',
                'order': 'relevance' if archive else 'date',
                'maxResults': max_results,
                'key': google_api_manager.get_key(),
            }
            
            if not archive:
                params['publishedAfter'] = (datetime.now() - timedelta(days=30)).isoformat() + 'Z'
            
            response = self.session.get(f"{self.base_url}/search", params=params)
            response.raise_for_status()
            
            data = response.json()
            videos = []
            
            for item in data.get('items', []):
                video_data = self._parse_video_data(item)
                if video_data:
                    videos.append(video_data)
            
            return videos
            
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 403:
                logger.warning(f"⚠️ خطأ 403 (Forbidden) في YouTube API. جاري تبديل المفتاح...")
                try:
                    google_api_manager.rotate_key()
                    logger.info("🔄 إعادة محاولة البحث بالمفتاح الجديد...")
                    return self._search_videos_by_query(query, max_results, archive)
                except Exception as rotation_error:
                    logger.critical(f"🚨 فشل في تبديل مفتاح API بعد خطأ بحث يوتيوب: {rotation_error}")
                    return self._alternative_youtube_search(query, max_results)
            else:
                logger.error(f"❌ فشل البحث في YouTube API للاستعلام: {query}", e)
                return self._alternative_youtube_search(query, max_results)
        except Exception as e:
            logger.error(f"❌ فشل البحث في YouTube API للاستعلام: {query}", e)
            return []
    
    def _alternative_youtube_search(self, query: str, max_results: int) -> List[Dict]:
        """بحث بديل بدون API (استخدام محرك البحث العام)"""
        try:
            # استخدام بحث عام عبر محرك البحث
            search_url = f"https://www.youtube.com/results?search_query={quote(query)}"
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = self.session.get(search_url, headers=headers)
            response.raise_for_status()
            
            # استخراج بيانات الفيديوهات من HTML
            videos = self._extract_videos_from_html(response.text, max_results)
            
            return videos
            
        except Exception as e:
            logger.error(f"❌ فشل البحث البديل في يوتيوب للاستعلام: {query}", e)
            return []
    
    def _extract_videos_from_html(self, html_content: str, max_results: int) -> List[Dict]:
        """استخراج بيانات الفيديوهات من HTML"""
        videos = []
        
        try:
            # البحث عن البيانات في JavaScript
            pattern = r'var ytInitialData = ({.*?});'
            matches = re.search(pattern, html_content)
            
            if matches:
                data = json.loads(matches.group(1))
                
                # التنقل عبر البيانات للعثور على الفيديوهات
                contents = self._extract_video_contents(data)
                
                for content in contents[:max_results]:
                    video_data = self._parse_search_result(content)
                    if video_data:
                        videos.append(video_data)
            
        except Exception as e:
            logger.debug(f"⚠️ فشل في استخراج الفيديوهات من HTML: {e}")
        
        return videos
    
    def _extract_video_contents(self, data: dict) -> List[Dict]:
        """استخراج محتويات الفيديوهات من بيانات YouTube"""
        contents = []
        
        try:
            # التنقل عبر هيكل البيانات المعقد
            if 'contents' in data:
                search_results = data['contents'].get('twoColumnSearchResultsRenderer', {})
                primary_contents = search_results.get('primaryContents', {})
                section_list = primary_contents.get('sectionListRenderer', {})
                
                for section in section_list.get('contents', []):
                    item_section = section.get('itemSectionRenderer', {})
                    
                    for item in item_section.get('contents', []):
                        if 'videoRenderer' in item:
                            contents.append(item['videoRenderer'])
        
        except Exception as e:
            logger.debug(f"⚠️ خطأ في استخراج محتويات الفيديوهات: {e}")
        
        return contents
    
    def _parse_search_result(self, video_data: dict) -> Optional[Dict]:
        """تحليل بيانات فيديو من نتائج البحث"""
        try:
            video_id = video_data.get('videoId')
            if not video_id:
                return None
            
            title_data = video_data.get('title', {})
            title = ""
            if 'runs' in title_data:
                title = ''.join([run.get('text', '') for run in title_data['runs']])
            elif 'simpleText' in title_data:
                title = title_data['simpleText']
            
            # استخراج اسم القناة
            channel_name = ""
            owner_text = video_data.get('ownerText', {})
            if 'runs' in owner_text:
                channel_name = owner_text['runs'][0].get('text', '')
            
            # استخراج تاريخ النشر
            published_date = self._extract_publish_date(video_data)
            
            # استخراج المدة
            duration = self._extract_duration(video_data)
            
            # استخراج عدد المشاهدات
            view_count = self._extract_view_count(video_data)
            
            return {
                'video_id': video_id,
                'title': title,
                'channel_name': channel_name,
                'published_at': published_date,
                'duration': duration,
                'view_count': view_count,
                'url': f"https://www.youtube.com/watch?v={video_id}",
                'thumbnail_url': f"https://img.youtube.com/vi/{video_id}/maxresdefault.jpg"
            }
            
        except Exception as e:
            logger.debug(f"⚠️ فشل في تحليل بيانات الفيديو: {e}")
            return None
    
    def _parse_video_data(self, item: dict) -> Optional[Dict]:
        """تحليل بيانات فيديو من YouTube API"""
        try:
            snippet = item.get('snippet', {})
            video_id = item.get('id', {}).get('videoId')
            
            if not video_id:
                return None
            
            return {
                'video_id': video_id,
                'title': snippet.get('title', ''),
                'channel_name': snippet.get('channelTitle', ''),
                'published_at': datetime.fromisoformat(snippet.get('publishedAt', '').replace('Z', '+00:00')),
                'description': snippet.get('description', ''),
                'thumbnail_url': snippet.get('thumbnails', {}).get('high', {}).get('url', ''),
                'url': f"https://www.youtube.com/watch?v={video_id}"
            }
            
        except Exception as e:
            logger.debug(f"⚠️ فشل في تحليل بيانات الفيديو من API: {e}")
            return None
    
    
    
    def _remove_duplicate_videos(self, videos: List[Dict]) -> List[Dict]:
        """إزالة الفيديوهات المكررة"""
        seen_ids = set()
        unique_videos = []
        
        for video in videos:
            video_id = video.get('video_id')
            if video_id and video_id not in seen_ids:
                seen_ids.add(video_id)
                unique_videos.append(video)
        
        return unique_videos
    
    def _extract_publish_date(self, video_data: dict) -> datetime:
        """استخراج تاريخ النشر من بيانات الفيديو"""
        try:
            published_text = video_data.get('publishedTimeText', {})
            if 'simpleText' in published_text:
                time_text = published_text['simpleText']
                return self._parse_relative_time(time_text)
        except:
            pass
        
        return datetime.now()
    
    def _parse_relative_time(self, time_text: str) -> datetime:
        """تحليل الوقت النسبي (مثل: منذ ساعتين، قبل يوم)"""
        now = datetime.now()
        time_text = time_text.lower()
        
        if 'hour' in time_text or 'ساعة' in time_text:
            hours = re.search(r'(\d+)', time_text)
            if hours:
                return now - timedelta(hours=int(hours.group(1)))
        
        elif 'day' in time_text or 'يوم' in time_text:
            days = re.search(r'(\d+)', time_text)
            if days:
                return now - timedelta(days=int(days.group(1)))
        
        elif 'week' in time_text or 'أسبوع' in time_text:
            weeks = re.search(r'(\d+)', time_text)
            if weeks:
                return now - timedelta(weeks=int(weeks.group(1)))
        
        elif 'month' in time_text or 'شهر' in time_text:
            months = re.search(r'(\d+)', time_text)
            if months:
                return now - timedelta(days=int(months.group(1)) * 30)
        
        return now
    
    def _extract_duration(self, video_data: dict) -> str:
        """استخراج مدة الفيديو"""
        try:
            length_text = video_data.get('lengthText', {})
            if 'simpleText' in length_text:
                return length_text['simpleText']
        except:
            pass
        
        return "غير محدد"
    
    def _extract_view_count(self, video_data: dict) -> int:
        """استخراج عدد المشاهدات"""
        try:
            view_text = video_data.get('viewCountText', {})
            if 'simpleText' in view_text:
                view_str = view_text['simpleText']
                # استخراج الأرقام من النص
                numbers = re.findall(r'[\d,]+', view_str)
                if numbers:
                    return int(numbers[0].replace(',', ''))
        except:
            pass
        
        return 0
    
    def get_transcript_from_txtify(self, video_url: str, txtify_url: str) -> Optional[str]:
        """الحصول على نص الفيديو من خدمة Txtify المستضافة ذاتيًا مع نظام احتياطي محسن"""
        if not txtify_url:
            logger.warning("⚠️ رابط خدمة Txtify غير محدد، التحول للطريقة الاحتياطية.")
            return self._fallback_transcript_extraction(video_url)

        # قائمة بالـ endpoints المحتملة
        endpoints = [
            f"{txtify_url.rstrip('/')}/transcribe_youtube",
            f"{txtify_url.rstrip('/')}/api/transcribe_youtube",
            f"{txtify_url.rstrip('/')}/transcribe",
            f"{txtify_url.rstrip('/')}/api/transcribe"
        ]

        for endpoint_url in endpoints:
            try:
                logger.info(f"📝 جارٍ طلب نص الفيديو من Txtify: {video_url} عبر {endpoint_url}")

                # تجربة طرق مختلفة لإرسال البيانات
                methods_to_try = [
                    # الطريقة الأولى: form data
                    {'data': {'youtube_url': video_url}},
                    # الطريقة الثانية: JSON
                    {'json': {'youtube_url': video_url}},
                    # الطريقة الثالثة: query parameters
                    {'params': {'youtube_url': video_url}},
                    # الطريقة الرابعة: form data مع اسم مختلف
                    {'data': {'url': video_url}},
                ]

                for method in methods_to_try:
                    try:
                        response = self.session.post(endpoint_url, timeout=300, **method)

                        # فحص حالة الاستجابة
                        if response.status_code == 200:
                            data = response.json()
                            transcript = data.get("transcript") or data.get("text") or data.get("result")

                            if transcript and len(transcript.strip()) > 10:
                                logger.info("✅ تم استلام النص بنجاح من Txtify.")
                                return transcript
                        elif response.status_code == 404:
                            logger.debug(f"🔍 Endpoint غير موجود: {endpoint_url}")
                            break  # جرب endpoint التالي
                        else:
                            logger.debug(f"⚠️ استجابة غير متوقعة: {response.status_code}")

                    except Exception as method_error:
                        logger.debug(f"🔄 فشلت طريقة الإرسال: {method_error}")
                        continue

            except Exception as e:
                logger.debug(f"❌ فشل endpoint {endpoint_url}: {e}")
                continue

        logger.warning("⚠️ فشلت جميع محاولات Txtify، التحول للطريقة الاحتياطية")
        return self._fallback_transcript_extraction(video_url)

    def _fallback_transcript_extraction(self, video_url: str) -> Optional[str]:
        """طريقة احتياطية لاستخراج النص من الفيديو"""
        try:
            # محاولة استخدام youtube-transcript-api
            try:
                from youtube_transcript_api import YouTubeTranscriptApi
                import re

                # استخراج video ID من الرابط
                video_id_match = re.search(r'(?:v=|\/)([0-9A-Za-z_-]{11}).*', video_url)
                if not video_id_match:
                    logger.warning("⚠️ لا يمكن استخراج video ID من الرابط")
                    return None

                video_id = video_id_match.group(1)

                # محاولة الحصول على النص بلغات مختلفة
                languages = ['ar', 'en', 'auto']
                for lang in languages:
                    try:
                        if lang == 'auto':
                            transcript_list = YouTubeTranscriptApi.get_transcript(video_id)
                        else:
                            transcript_list = YouTubeTranscriptApi.get_transcript(video_id, languages=[lang])

                        # تجميع النص
                        full_text = ' '.join([item['text'] for item in transcript_list])

                        if len(full_text.strip()) > 50:
                            logger.info(f"✅ تم استخراج النص باستخدام youtube-transcript-api (لغة: {lang})")
                            return full_text

                    except Exception as lang_error:
                        logger.debug(f"🔄 فشل استخراج النص بلغة {lang}: {lang_error}")
                        continue

            except ImportError:
                logger.debug("📦 youtube-transcript-api غير مثبت")
            except Exception as transcript_error:
                logger.debug(f"❌ فشل youtube-transcript-api: {transcript_error}")

            # الطريقة الاحتياطية الأخيرة: استخدام معلومات الفيديو الأساسية
            logger.info("🔄 استخدام الطريقة الاحتياطية الأساسية (العنوان والوصف)")
            return None

        except Exception as e:
            logger.error(f"❌ فشلت جميع طرق استخراج النص: {e}")
            return None

    def generate_article_from_video(self, video: Dict, txtify_url: str = None) -> Optional[Dict]:
        """توليد مقال من بيانات الفيديو، مع محاولة استخدام النص المستخرج"""
        try:
            title = video['title']
            video_url = video['url']
            channel_name = video.get('channel_name', 'غير محدد')

            # محاولة الحصول على النص الكامل للفيديو
            transcript = self.get_transcript_from_txtify(video_url, txtify_url)

            # توليد محتوى المقال
            if transcript:
                # إذا نجحنا في الحصول على النص، نستخدمه
                logger.info("✨ استخدام النص الكامل من Txtify لتوليد المحتوى.")
                article_content = transcript
            else:
                # إذا فشلنا، نعود إلى الطريقة القديمة
                logger.info("⚠️ فشل في الحصول على النص، استخدام الطريقة القديمة (العنوان والوصف).")
                article_content = self._generate_content_from_video_title(title, video_url, channel_name)

            # استخراج الكلمات المفتاحية
            keywords = self._extract_keywords_from_title(title)

            # تحديد نوع المحتوى
            content_type = self._determine_content_type(title)

            return {
                'title': title,
                'content': article_content,
                'source_url': video_url,
                'source_type': 'youtube',
                'channel_name': channel_name,
                'published_date': video.get('published_at', datetime.now()),
                'keywords': keywords,
                'content_type': content_type,
                'thumbnail_url': video.get('thumbnail_url')
            }

        except Exception as e:
            logger.error(f"❌ فشل في توليد مقال من الفيديو: {video.get('title', 'غير محدد')}", e)
            return None
    
    def _generate_content_from_video_title(self, title: str, video_url: str, channel_name: str) -> str:
        """توليد محتوى مقال من عنوان الفيديو"""
        # هذا محتوى أساسي، سيتم تحسينه بواسطة Gemini لاحقاً
        content_template = f"""
تم الكشف عن معلومات جديدة ومثيرة حول عالم الألعاب من خلال فيديو جديد نشرته قناة {channel_name}.

العنوان: {title}

يتضمن هذا المحتوى آخر التطورات والأخبار المتعلقة بعالم الألعاب، والتي قد تشمل:
- تحديثات جديدة للألعاب
- إصدارات ألعاب جديدة
- عروض دعائية وأسلوب لعب
- مراجعات وتحليلات
- أخبار من مطوري الألعاب

لمشاهدة الفيديو الكامل والحصول على التفاصيل الشاملة، يمكنكم زيارة الرابط التالي:
{video_url}

تابعوا قناة {channel_name} للحصول على آخر أخبار وتطورات عالم الألعاب.
        """

        return content_template.strip()
    
    def _extract_keywords_from_title(self, title: str) -> List[str]:
        """استخراج الكلمات المفتاحية من عنوان الفيديو"""
        keywords = ['ألعاب', 'gaming']

        # كلمات مفتاحية شائعة في محتوى الألعاب
        common_keywords = {
            'update': 'تحديث',
            'review': 'مراجعة',
            'gameplay': 'أسلوب لعب',
            'trailer': 'عرض دعائي',
            'new': 'جديد',
            'latest': 'أحدث',
            'news': 'أخبار',
            'guide': 'دليل',
            'tips': 'نصائح',
            'tricks': 'حيل'
        }

        title_lower = title.lower()

        for eng_word, ar_word in common_keywords.items():
            if eng_word in title_lower:
                keywords.append(ar_word)
            if ar_word in title_lower:
                keywords.append(ar_word)

        # إضافة كلمات محددة من العنوان
        title_words = re.findall(r'\b\w+\b', title_lower)
        for word in title_words:
            if len(word) > 3 and word not in keywords:
                keywords.append(word)

        return keywords[:8]  # الحد الأقصى 8 كلمات مفتاحية
    
    def _determine_content_type(self, title: str) -> str:
        """تحديد نوع المحتوى بناءً على العنوان"""
        title_lower = title.lower()

        if any(word in title_lower for word in ['update', 'تحديث', 'new', 'جديد']):
            return 'أخبار_التحديثات'
        elif any(word in title_lower for word in ['review', 'مراجعة']):
            return 'المراجعات_التحليلات'
        elif any(word in title_lower for word in ['gameplay', 'أسلوب لعب']):
            return 'أخبار_عامة' # دمج عروض اللعب في الأخبار العامة
        elif any(word in title_lower for word in ['trailer', 'عرض دعائي']):
            return 'أخبار_عامة' # دمج العروض في الأخبار العامة
        else:
            return 'أخبار_عامة'
